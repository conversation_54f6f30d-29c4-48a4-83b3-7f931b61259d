package com.cet.electric.vpp.resourcemanager.web;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/10/17 09:50
 * @description: Virtual Power Plant Resource Manager Application
 */
@SpringBootApplication(
    scanBasePackages = {
        "com.cet.electric.vpp.resourcemanager",
        "com.cet.eem.fusion.common"
    },
    exclude = {
        // 如果需要排除某些自动配置，可以在这里添加
    }
)
@EnableFeignClients(basePackages = {
    "com.cet.eem.fusion.common.feign",
    "com.cet.electric.vpp.resourcemanager"
})
public class ResourceManagerApplication {
    public static void main(String[] args) {
        SpringApplication.run(ResourceManagerApplication.class, args);
    }
}
