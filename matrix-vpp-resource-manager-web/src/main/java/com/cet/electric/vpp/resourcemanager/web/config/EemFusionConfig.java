package com.cet.electric.vpp.resourcemanager.web.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * EEM Fusion相关配置
 *
 * <AUTHOR>
 * @date 2025/01/01
 */
@Configuration
public class EemFusionConfig {

    /**
     * 创建OnlyReportService的默认实现Bean
     * 使用具体的类名来避免类型冲突
     */
    @Bean("onlyReportService")
    @ConditionalOnMissingBean
    @Primary
    public OnlyReportServiceImpl onlyReportService() {
        return new OnlyReportServiceImpl();
    }

    /**
     * OnlyReportService的默认实现类
     */
    public static class OnlyReportServiceImpl {
        // 提供默认的空实现方法
        // 具体方法需要根据实际的OnlyReportService接口来实现

        public void reportData(Object data) {
            // 默认空实现
            System.out.println("OnlyReportService.reportData called with: " + data);
        }

        public Object queryData(String query) {
            // 默认空实现
            System.out.println("OnlyReportService.queryData called with: " + query);
            return null;
        }
    }
}
