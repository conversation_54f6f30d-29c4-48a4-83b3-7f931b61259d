package com.cet.electric.vpp.resourcemanager.web.config;

import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

/**
 * Feign客户端配置类
 * 
 * <AUTHOR>
 * @date 2025/01/01
 */
@Configuration
@EnableFeignClients(basePackages = {
    "com.cet.eem.fusion.common.feign",
    "com.cet.electric.vpp.resourcemanager"
})
public class FeignConfig {
    
    // Feign客户端配置可以在这里添加
    // 例如：超时配置、编码器、解码器等
    
}
