// @AI-Generated-start
package com.cet.electric.vpp.resourcemanager.core.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;

import com.cet.electric.vpp.resourcemanager.common.bean.Result;
import com.cet.electric.vpp.resourcemanager.common.bean.bo.ResourceBO;
import com.cet.electric.vpp.resourcemanager.common.bean.bo.SiteBO;
import com.cet.electric.vpp.resourcemanager.common.bean.dto.ResourceDTO;
import com.cet.electric.vpp.resourcemanager.common.bean.dto.ResourceQueryDTO;
import com.cet.electric.vpp.resourcemanager.common.common.PageResult;
import com.cet.electric.vpp.resourcemanager.core.mapper.ResourceMapper;
import com.cet.electric.vpp.resourcemanager.core.mapper.SiteMapper;
import com.cet.electric.vpp.resourcemanager.core.service.ResourceService;
import com.cet.electric.modelservice.sdk.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 资源服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
public class ResourceServiceImpl implements ResourceService {

    @Autowired
    private ResourceMapper resourceMapper;
    
    @Autowired
    private SiteMapper siteMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<ResourceBO> createResource(ResourceDTO resourceDTO) {
        try {
            log.info("创建资源，参数：{}", resourceDTO);
            
            if (resourceDTO == null) {
                return Result.error("参数不能为空");
            }
            
            // 检查编码是否重复
            if (StrUtil.isNotBlank(resourceDTO.getCode())) {
                Result<Boolean> checkResult = checkResourceCodeExists(resourceDTO.getCode());
                if (checkResult.getData() != null && checkResult.getData()) {
                    return Result.error("资源编码已存在");
                }
            }
            
            // 创建ResourceBO对象
            ResourceBO resourceBO = new ResourceBO();
            BeanUtil.copyProperties(resourceDTO, resourceBO);
            resourceBO.setCreateTime(LocalDateTime.now());
            resourceBO.setUpdateTime(LocalDateTime.now());
            resourceBO.setDeleted(0);
            
            // 保存数据
            resourceMapper.insert(resourceBO);
            
            log.info("资源创建成功，ID：{}", resourceBO.getId());
            return Result.success(resourceBO);
            
        } catch (Exception e) {
            log.error("创建资源失败", e);
            return Result.error("创建资源失败：" + e.getMessage());
        }
    }

    @Override
    public Result<ResourceBO> getResourceById(Long id) {
        try {
            log.info("查询资源详情，ID：{}", id);
            
            if (id == null) {
                return Result.error("资源ID不能为空");
            }
            
            // 查询数据
            ResourceBO resourceBO = resourceMapper.selectById(id);
            
            if (resourceBO == null || resourceBO.getDeleted() == 1) {
                return Result.error("资源不存在");
            }
            
            return Result.success(resourceBO);
            
        } catch (Exception e) {
            log.error("查询资源详情失败", e);
            return Result.error("查询资源详情失败：" + e.getMessage());
        }
    }

    @Override
    public Result<ResourceBO> getResourceByCode(String code) {
        try {
            log.info("根据编码查询资源，编码：{}", code);
            
            if (code == null || code.trim().isEmpty()) {
                return Result.error("资源编码不能为空");
            }
            
            // 构建查询条件
            LambdaQueryWrapper<ResourceBO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ResourceBO::getCode, code)
                   .eq(ResourceBO::getDeleted, 0);
            
            ResourceBO resourceBO = resourceMapper.selectOne(wrapper);
            
            if (resourceBO == null) {
                return Result.error("资源不存在");
            }
            
            return Result.success(resourceBO);
            
        } catch (Exception e) {
            log.error("根据编码查询资源失败", e);
            return Result.error("根据编码查询资源失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<ResourceBO> updateResource(Long id, ResourceDTO resourceDTO) {
        try {
            log.info("更新资源，ID：{}，参数：{}", id, resourceDTO);
            
            if (id == null || resourceDTO == null) {
                return Result.error("参数不能为空");
            }
            
            // 检查资源是否存在
            Result<ResourceBO> getResult = getResourceById(id);
            if (getResult.getData() == null) {
                return Result.error("资源不存在");
            }
            
            // 更新数据
            ResourceBO resourceBO = getResult.getData();
            BeanUtil.copyProperties(resourceDTO, resourceBO);
            resourceBO.setUpdateTime(LocalDateTime.now());
            
            // 更新数据
            resourceMapper.insert(resourceBO);
            
            log.info("资源更新成功，ID：{}", id);
            return Result.success(resourceBO);
            
        } catch (Exception e) {
            log.error("更新资源失败", e);
            return Result.error("更新资源失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> deleteResource(Long id) {
        try {
            log.info("删除资源，ID：{}", id);
            
            if (id == null) {
                return Result.error("资源ID不能为空");
            }
            
            // 检查资源是否存在
            Result<ResourceBO> getResult = getResourceById(id);
            if (getResult.getData() == null) {
                return Result.error("资源不存在");
            }
            
            // 逻辑删除
            ResourceBO resourceBO = getResult.getData();
            resourceBO.setDeleted(1);
            resourceBO.setUpdateTime(LocalDateTime.now());
            resourceMapper.insert(resourceBO);
            
            log.info("资源删除成功，ID：{}", id);
            return Result.success(true);
            
        } catch (Exception e) {
            log.error("删除资源失败", e);
            return Result.error("删除资源失败：" + e.getMessage());
        }
    }

    @Override
    public Result<PageResult<ResourceBO>> getResourceList(ResourceQueryDTO queryDTO) {
        try {
            log.info("分页查询资源列表，参数：{}", queryDTO);
            
            // 构建查询条件
            LambdaQueryWrapper<ResourceBO> wrapper = new LambdaQueryWrapper<>();
            
            if (StringUtils.hasText(queryDTO.getName())) {
                wrapper.like(ResourceBO::getName, queryDTO.getName());
            }
            if (StringUtils.hasText(queryDTO.getCode())) {
                wrapper.eq(ResourceBO::getCode, queryDTO.getCode());
            }
            if (queryDTO.getType() != null) {
                wrapper.eq(ResourceBO::getType, queryDTO.getType());
            }
            if (queryDTO.getVppId() != null) {
                wrapper.eq(ResourceBO::getVppId, queryDTO.getVppId());
            }
            if (queryDTO.getSiteId() != null) {
                wrapper.eq(ResourceBO::getSiteId, queryDTO.getSiteId());
            }
            if (queryDTO.getStatus() != null) {
                wrapper.eq(ResourceBO::getStatus, queryDTO.getStatus());
            }
            if (StringUtils.hasText(queryDTO.getLocation())) {
                wrapper.like(ResourceBO::getLocation, queryDTO.getLocation());
            }
            if (StringUtils.hasText(queryDTO.getManufacturer())) {
                wrapper.like(ResourceBO::getManufacturer, queryDTO.getManufacturer());
            }
            wrapper.eq(ResourceBO::getDeleted, 0);
            
            // 排序
            if (StringUtils.hasText(queryDTO.getOrderBy())) {
                if ("desc".equalsIgnoreCase(queryDTO.getOrderDirection())) {
                    wrapper.orderByDesc(ResourceBO::getCreateTime);
                } else {
                    wrapper.orderByAsc(ResourceBO::getCreateTime);
                }
            } else {
                wrapper.orderByDesc(ResourceBO::getCreateTime);
            }
            
            // 查询总数
            long total = resourceMapper.selectCount(wrapper);
            
            // 分页查询 - 使用 LIMIT 语法
            int offset = (queryDTO.getPageNum() - 1) * queryDTO.getPageSize();
            int limit = queryDTO.getPageSize();
            
            // 创建新的查询条件用于分页
            LambdaQueryWrapper<ResourceBO> pageWrapper = new LambdaQueryWrapper<>();
            pageWrapper.eq(ResourceBO::getDeleted, 0);
            
            if (StringUtils.hasText(queryDTO.getName())) {
                pageWrapper.like(ResourceBO::getName, queryDTO.getName());
            }
            if (StringUtils.hasText(queryDTO.getCode())) {
                pageWrapper.eq(ResourceBO::getCode, queryDTO.getCode());
            }
            if (queryDTO.getType() != null) {
                pageWrapper.eq(ResourceBO::getType, queryDTO.getType());
            }
            if (queryDTO.getVppId() != null) {
                pageWrapper.eq(ResourceBO::getVppId, queryDTO.getVppId());
            }
            if (queryDTO.getSiteId() != null) {
                pageWrapper.eq(ResourceBO::getSiteId, queryDTO.getSiteId());
            }
            if (queryDTO.getStatus() != null) {
                pageWrapper.eq(ResourceBO::getStatus, queryDTO.getStatus());
            }
            if (StringUtils.hasText(queryDTO.getLocation())) {
                pageWrapper.like(ResourceBO::getLocation, queryDTO.getLocation());
            }
            if (StringUtils.hasText(queryDTO.getManufacturer())) {
                pageWrapper.like(ResourceBO::getManufacturer, queryDTO.getManufacturer());
            }
            
            // 排序
            if (StringUtils.hasText(queryDTO.getOrderBy())) {
                if ("desc".equalsIgnoreCase(queryDTO.getOrderDirection())) {
                    pageWrapper.orderByDesc(ResourceBO::getCreateTime);
                } else {
                    pageWrapper.orderByAsc(ResourceBO::getCreateTime);
                }
            } else {
                pageWrapper.orderByDesc(ResourceBO::getCreateTime);
            }
            
            // 手动分页
            List<ResourceBO> allList = resourceMapper.selectList(pageWrapper);
            List<ResourceBO> list = new ArrayList<>();
            if (allList != null && !allList.isEmpty()) {
                int startIndex = Math.min(offset, allList.size());
                int endIndex = Math.min(startIndex + limit, allList.size());
                if (startIndex < endIndex) {
                    list = allList.subList(startIndex, endIndex);
                }
            }
            
            if (list == null) {
                list = new ArrayList<>();
            }
            
            PageResult<ResourceBO> pageResult = PageResult.of(
                list, 
                total, 
                queryDTO.getPageNum(), 
                queryDTO.getPageSize()
            );
            
            return Result.success(pageResult);
            
        } catch (Exception e) {
            log.error("分页查询资源列表失败", e);
            return Result.error("分页查询资源列表失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<ResourceBO>> getAllResources() {
        try {
            log.info("查询所有资源列表");
            
            // 构建查询条件
            LambdaQueryWrapper<ResourceBO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ResourceBO::getDeleted, 0)
                   .orderByDesc(ResourceBO::getCreateTime);
            
            List<ResourceBO> list = resourceMapper.selectList(wrapper);
            
            if (list == null) {
                list = new ArrayList<>();
            }
            
            return Result.success(list);
            
        } catch (Exception e) {
            log.error("查询所有资源列表失败", e);
            return Result.error("查询所有资源列表失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<ResourceBO>> getResourcesByVppId(Long vppId) {
        try {
            log.info("根据VPP ID获取资源列表，VPP ID：{}", vppId);
            
            if (vppId == null) {
                return Result.error("VPP ID不能为空");
            }
            
            // 构建查询条件
            LambdaQueryWrapper<ResourceBO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ResourceBO::getVppId, vppId)
                   .eq(ResourceBO::getDeleted, 0)
                   .orderByDesc(ResourceBO::getCreateTime);
            
            List<ResourceBO> list = resourceMapper.selectList(wrapper);
            
            if (list == null) {
                list = new ArrayList<>();
            }
            
            return Result.success(list);
            
        } catch (Exception e) {
            log.error("根据VPP ID获取资源列表失败", e);
            return Result.error("根据VPP ID获取资源列表失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<ResourceBO>> getResourcesByType(Integer type) {
        try {
            log.info("根据类型查询资源列表，类型：{}", type);
            
            if (type == null) {
                return Result.error("资源类型不能为空");
            }
            
            // 构建查询条件
            LambdaQueryWrapper<ResourceBO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ResourceBO::getType, type)
                   .eq(ResourceBO::getDeleted, 0)
                   .orderByDesc(ResourceBO::getCreateTime);
            
            List<ResourceBO> list = resourceMapper.selectList(wrapper);
            
            if (list == null) {
                list = new ArrayList<>();
            }
            
            return Result.success(list);
            
        } catch (Exception e) {
            log.error("根据类型查询资源列表失败", e);
            return Result.error("根据类型查询资源列表失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<ResourceBO>> getResourcesByStatus(Integer status) {
        try {
            log.info("根据状态查询资源列表，状态：{}", status);
            
            if (status == null) {
                return Result.error("资源状态不能为空");
            }
            
            // 构建查询条件
            LambdaQueryWrapper<ResourceBO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ResourceBO::getStatus, status)
                   .eq(ResourceBO::getDeleted, 0)
                   .orderByDesc(ResourceBO::getCreateTime);
            
            List<ResourceBO> list = resourceMapper.selectList(wrapper);
            
            if (list == null) {
                list = new ArrayList<>();
            }
            
            return Result.success(list);
            
        } catch (Exception e) {
            log.error("根据状态查询资源列表失败", e);
            return Result.error("根据状态查询资源列表失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Boolean> checkResourceCodeExists(String code) {
        try {
            log.info("检查资源编码是否存在，编码：{}", code);
            
            if (code == null || code.trim().isEmpty()) {
                return Result.error("资源编码不能为空");
            }
            
            // 构建查询条件
            LambdaQueryWrapper<ResourceBO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ResourceBO::getCode, code)
                   .eq(ResourceBO::getDeleted, 0);
            
            long count = resourceMapper.selectCount(wrapper);
            boolean exists = count > 0;
            
            return Result.success(exists);
            
        } catch (Exception e) {
            log.error("检查资源编码是否存在失败", e);
            return Result.error("检查资源编码是否存在失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Long> countResources() {
        try {
            log.info("统计资源数量");
            
            // 构建查询条件
            LambdaQueryWrapper<ResourceBO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ResourceBO::getDeleted, 0);
            
            long count = resourceMapper.selectCount(wrapper);
            
            return Result.success(count);
            
        } catch (Exception e) {
            log.error("统计资源数量失败", e);
            return Result.error("统计资源数量失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<SiteBO>> getSitesByResource(Long resourceId) {
        try {
            log.info("获取资源下站点列表，资源ID：{}", resourceId);
            
            if (resourceId == null) {
                return Result.error("资源ID不能为空");
            }
            
            // 先查询资源信息获取站点ID
            ResourceBO resourceBO = resourceMapper.selectById(resourceId);
            if (resourceBO == null || resourceBO.getDeleted() == 1) {
                return Result.error("资源不存在");
            }
            
            if (resourceBO.getSiteId() == null) {
                return Result.success(new ArrayList<>());
            }
            
            // 根据站点ID查询站点信息
            LambdaQueryWrapper<SiteBO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SiteBO::getId, resourceBO.getSiteId())
                   .eq(SiteBO::getDeleted, false);
            
            List<SiteBO> list = siteMapper.selectList(wrapper);
            
            if (list == null) {
                list = new ArrayList<>();
            }
            
            return Result.success(list);
            
        } catch (Exception e) {
            log.error("获取资源下站点列表失败", e);
            return Result.error("获取资源下站点列表失败：" + e.getMessage());
        }
    }
}
// @AI-Generated-end 