// @AI-Generated-start
package com.cet.electric.vpp.resourcemanager.core.controller;


import com.cet.electric.vpp.resourcemanager.common.bean.Result;
import com.cet.electric.vpp.resourcemanager.common.bean.bo.ResourceBO;
import com.cet.electric.vpp.resourcemanager.common.bean.bo.UserBO;
import com.cet.electric.vpp.resourcemanager.common.bean.dto.UserDTO;
import com.cet.electric.vpp.resourcemanager.common.bean.dto.UserQueryDTO;
import com.cet.electric.vpp.resourcemanager.common.common.PageResult;
import com.cet.electric.vpp.resourcemanager.core.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 用户控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/vpp/resource-manager/user-config")
@Api(tags = "用户管理", description = "用户相关接口")
@Validated
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 创建用户
     *
     * @param userDTO 用户信息
     * @return 创建结果
     */
    @PostMapping
    @ApiOperation(value = "创建用户", notes = "创建新用户")
    public Result<UserBO> createUser(
            @ApiParam(value = "用户信息", required = true)
            @Valid @RequestBody UserDTO userDTO) {
        log.info("创建用户请求，参数：{}", userDTO);
        return userService.createUser(userDTO);
    }

    /**
     * 根据ID查询用户
     *
     * @param id 用户ID
     * @return 用户信息
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询用户", notes = "根据用户ID查询详细信息")
    public Result<UserBO> getUserById(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable @NotNull(message = "用户ID不能为空") Long id) {
        log.info("查询用户请求，ID：{}", id);
        return userService.getUserById(id);
    }

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    @GetMapping("/username/{username}")
    @ApiOperation(value = "根据用户名查询用户", notes = "根据用户名查询详细信息")
    public Result<UserBO> getUserByUsername(
            @ApiParam(value = "用户名", required = true)
            @PathVariable @NotNull(message = "用户名不能为空") String username) {
        log.info("根据用户名查询用户请求，用户名：{}", username);
        return userService.getUserByUsername(username);
    }

    /**
     * 更新用户信息
     *
     * @param id      用户ID
     * @param userDTO 用户信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新用户", notes = "更新用户信息")
    public Result<UserBO> updateUser(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable @NotNull(message = "用户ID不能为空") Long id,
            @ApiParam(value = "用户信息", required = true)
            @Valid @RequestBody UserDTO userDTO) {
        log.info("更新用户请求，ID：{}，参数：{}", id, userDTO);
        return userService.updateUser(id, userDTO);
    }

    /**
     * 删除用户
     *
     * @param id 用户ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除用户", notes = "删除用户")
    public Result<Boolean> deleteUser(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable @NotNull(message = "用户ID不能为空") Long id) {
        log.info("删除用户请求，ID：{}", id);
        return userService.deleteUser(id);
    }

    /**
     * 分页查询用户列表
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询用户列表", notes = "根据条件分页查询用户列表")
    public Result<PageResult<UserBO>> getUserPage(
            @ApiParam(value = "查询条件", required = true)
            @Valid @RequestBody UserQueryDTO queryDTO) {
        log.info("分页查询用户列表请求，参数：{}", queryDTO);
        return userService.getUserList(queryDTO);
    }

    /**
     * 查询所有用户列表
     *
     * @return 用户列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询所有用户列表", notes = "查询所有用户列表（不分页）")
    public Result<List<UserBO>> getAllUsers() {
        log.info("查询所有用户列表请求");
        return userService.getAllUsers();
    }

    /**
     * 根据VPP查询用户列表
     *
     * @param vppId VPP ID
     * @return 用户列表
     */
    @GetMapping("/vpp/{vppId}")
    @ApiOperation(value = "根据VPP查询用户列表", notes = "根据VPP ID查询用户列表")
    public Result<List<UserBO>> getUsersByVpp(
            @ApiParam(value = "VPP ID", required = true)
            @PathVariable @NotNull(message = "VPP ID不能为空") Long vppId) {
        log.info("根据VPP查询用户列表请求，VPP ID：{}", vppId);
        return userService.getUsersByVppId(vppId);
    }

    /**
     * 根据角色查询用户列表
     *
     * @param role 用户角色
     * @return 用户列表
     */
    @GetMapping("/role/{role}")
    @ApiOperation(value = "根据角色查询用户列表", notes = "根据用户角色查询列表")
    public Result<List<UserBO>> getUsersByRole(
            @ApiParam(value = "用户角色", required = true)
            @PathVariable @NotNull(message = "用户角色不能为空") Integer role) {
        log.info("根据角色查询用户列表请求，角色：{}", role);
        return userService.getUsersByRole(role);
    }

    /**
     * 根据状态查询用户列表
     *
     * @param status 用户状态
     * @return 用户列表
     */
    @GetMapping("/status/{status}")
    @ApiOperation(value = "根据状态查询用户列表", notes = "根据用户状态查询列表")
    public Result<List<UserBO>> getUsersByStatus(
            @ApiParam(value = "用户状态", required = true)
            @PathVariable @NotNull(message = "用户状态不能为空") Integer status) {
        log.info("根据状态查询用户列表请求，状态：{}", status);
        return userService.getUsersByStatus(status);
    }

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @return 是否存在
     */
    @GetMapping("/check-username/{username}")
    @ApiOperation(value = "检查用户名是否存在", notes = "检查用户名是否已存在")
    public Result<Boolean> checkUsernameExists(
            @ApiParam(value = "用户名", required = true)
            @PathVariable @NotNull(message = "用户名不能为空") String username) {
        log.info("检查用户名是否存在请求，用户名：{}", username);
        return userService.checkUsernameExists(username);
    }

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @return 是否存在
     */
    @GetMapping("/check-email/{email}")
    @ApiOperation(value = "检查邮箱是否存在", notes = "检查邮箱是否已存在")
    public Result<Boolean> checkEmailExists(
            @ApiParam(value = "邮箱", required = true)
            @PathVariable @NotNull(message = "邮箱不能为空") String email) {
        log.info("检查邮箱是否存在请求，邮箱：{}", email);
        return userService.checkEmailExists(email);
    }

    /**
     * 重置用户密码
     *
     * @param id 用户ID
     * @return 重置结果
     */
    @PutMapping("/{id}/reset-password")
    @ApiOperation(value = "重置用户密码", notes = "重置用户密码为默认密码")
    public Result<Boolean> resetPassword(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable @NotNull(message = "用户ID不能为空") Long id) {
        log.info("重置用户密码请求，ID：{}", id);
        return userService.resetPassword(id);
    }

    /**
     * 修改用户状态
     *
     * @param id     用户ID
     * @param status 新状态
     * @return 修改结果
     */
    @PutMapping("/{id}/status/{status}")
    @ApiOperation(value = "修改用户状态", notes = "修改用户状态（启用/禁用）")
    public Result<Boolean> updateUserStatus(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable @NotNull(message = "用户ID不能为空") Long id,
            @ApiParam(value = "用户状态", required = true)
            @PathVariable @NotNull(message = "用户状态不能为空") Integer status) {
        log.info("修改用户状态请求，ID：{}，状态：{}", id, status);
        return userService.updateUserStatus(id, status);
    }

    /**
     * 获取用户管理的资源列表
     *
     * @param id 用户ID
     * @return 资源列表
     */
    @GetMapping("/{id}/resources")
    @ApiOperation(value = "获取用户管理的资源列表", notes = "获取用户管理的资源列表")
    public Result<List<ResourceBO>> getUserResources(
            @ApiParam(value = "用户ID", required = true)
            @PathVariable @NotNull(message = "用户ID不能为空") Long id) {
        log.info("获取用户管理的资源列表请求，用户ID：{}", id);
        return userService.getUserResources(id);
    }
}
// @AI-Generated-end 
