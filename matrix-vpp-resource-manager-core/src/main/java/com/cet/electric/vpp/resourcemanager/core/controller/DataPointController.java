// @AI-Generated-start
package com.cet.electric.vpp.resourcemanager.core.controller;


import com.cet.electric.vpp.resourcemanager.common.bean.Result;
import com.cet.electric.vpp.resourcemanager.common.bean.bo.DataPointBO;
import com.cet.electric.vpp.resourcemanager.common.bean.dto.DataPointDTO;
import com.cet.electric.vpp.resourcemanager.common.bean.dto.DataPointQueryDTO;
import com.cet.electric.vpp.resourcemanager.common.common.PageResult;
import com.cet.electric.vpp.resourcemanager.core.service.DataPointService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 数据点管理控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/vpp/resource-manager/datapoint-config")
@Api(tags = "数据点管理", description = "数据点相关接口")
@Validated
public class DataPointController {
    
    @Autowired
    private DataPointService dataPointService;
    
    @PostMapping
    @ApiOperation(value = "创建数据点", notes = "创建新数据点")
    public Result<DataPointBO> createDataPoint(
            @ApiParam(value = "数据点信息", required = true)
            @Valid @RequestBody DataPointDTO dataPointDTO) {
        log.info("创建数据点请求，参数：{}", dataPointDTO);
        return dataPointService.createDataPoint(dataPointDTO);
    }
    
    @GetMapping("/{id}")
    @ApiOperation(value = "获取数据点详情", notes = "根据ID获取数据点详细信息")
    public Result<DataPointBO> getDataPointById(
            @ApiParam(value = "数据点ID", required = true)
            @PathVariable @NotNull(message = "数据点ID不能为空") Long id) {
        log.info("获取数据点详情请求，ID：{}", id);
        return dataPointService.getDataPointById(id);
    }
    
    @GetMapping("/code/{code}")
    @ApiOperation(value = "根据编码获取数据点", notes = "根据编码获取数据点详细信息")
    public Result<DataPointBO> getDataPointByCode(
            @ApiParam(value = "数据点编码", required = true)
            @PathVariable @NotNull(message = "数据点编码不能为空") String code) {
        log.info("根据编码获取数据点请求，编码：{}", code);
        return dataPointService.getDataPointByCode(code);
    }
    
    @PutMapping("/{id}")
    @ApiOperation(value = "更新数据点配置", notes = "更新数据点配置信息")
    public Result<DataPointBO> updateDataPoint(
            @ApiParam(value = "数据点ID", required = true)
            @PathVariable @NotNull(message = "数据点ID不能为空") Long id,
            @ApiParam(value = "数据点信息", required = true)
            @Valid @RequestBody DataPointDTO dataPointDTO) {
        log.info("更新数据点配置请求，ID：{}，参数：{}", id, dataPointDTO);
        return dataPointService.updateDataPoint(id, dataPointDTO);
    }
    
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除数据点关联", notes = "删除指定的数据点关联")
    public Result<Boolean> deleteDataPoint(
            @ApiParam(value = "数据点ID", required = true)
            @PathVariable @NotNull(message = "数据点ID不能为空") Long id) {
        log.info("删除数据点关联请求，ID：{}", id);
        return dataPointService.deleteDataPoint(id);
    }
    
    @PostMapping("/page")
    @ApiOperation(value = "分页查询数据点列表", notes = "分页查询数据点列表")
    public Result<PageResult<DataPointBO>> getDataPointList(
            @ApiParam(value = "查询条件", required = true)
            @Valid @RequestBody DataPointQueryDTO queryDTO) {
        log.info("分页查询数据点列表请求，参数：{}", queryDTO);
        return dataPointService.getDataPointList(queryDTO);
    }
    
    @GetMapping("/list")
    @ApiOperation(value = "获取所有数据点列表", notes = "获取所有数据点列表")
    public Result<List<DataPointBO>> getAllDataPoints() {
        log.info("获取所有数据点列表请求");
        return dataPointService.getAllDataPoints();
    }
    
    @GetMapping("/device/{deviceId}")
    @ApiOperation(value = "获取设备数据点列表", notes = "根据设备ID获取数据点列表")
    public Result<List<DataPointBO>> getDataPointsByDevice(
            @ApiParam(value = "设备ID", required = true)
            @PathVariable @NotNull(message = "设备ID不能为空") Long deviceId) {
        log.info("获取设备数据点列表请求，设备ID：{}", deviceId);
        return Result.success(dataPointService.listDataPointsByDevice(deviceId));
    }
    
    @GetMapping("/site/{siteId}")
    @ApiOperation(value = "获取站点数据点列表", notes = "根据站点ID获取数据点列表")
    public Result<List<DataPointBO>> getDataPointsBySite(
            @ApiParam(value = "站点ID", required = true)
            @PathVariable @NotNull(message = "站点ID不能为空") Long siteId) {
        log.info("获取站点数据点列表请求，站点ID：{}", siteId);
        return Result.success(dataPointService.listDataPointsBySite(siteId));
    }
    
    @GetMapping("/resource/{resourceId}")
    @ApiOperation(value = "根据资源获取数据点列表", notes = "根据资源ID获取数据点列表")
    public Result<List<DataPointBO>> getDataPointsByResource(
            @ApiParam(value = "资源ID", required = true)
            @PathVariable @NotNull(message = "资源ID不能为空") Long resourceId) {
        log.info("根据资源获取数据点列表请求，资源ID：{}", resourceId);
        return dataPointService.getDataPointsByResource(resourceId);
    }
    
    @GetMapping("/type/{type}")
    @ApiOperation(value = "根据类型获取数据点列表", notes = "根据数据点类型获取列表")
    public Result<List<DataPointBO>> getDataPointsByType(
            @ApiParam(value = "数据点类型", required = true)
            @PathVariable @NotNull(message = "数据点类型不能为空") Integer type) {
        log.info("根据类型获取数据点列表请求，类型：{}", type);
        return dataPointService.getDataPointsByType(type);
    }
    
    @GetMapping("/status/{status}")
    @ApiOperation(value = "根据状态获取数据点列表", notes = "根据数据点状态获取列表")
    public Result<List<DataPointBO>> getDataPointsByStatus(
            @ApiParam(value = "数据点状态", required = true)
            @PathVariable @NotNull(message = "数据点状态不能为空") Integer status) {
        log.info("根据状态获取数据点列表请求，状态：{}", status);
        return dataPointService.getDataPointsByStatus(status);
    }
    
    @GetMapping("/check-code/{code}")
    @ApiOperation(value = "检查数据点编码是否存在", notes = "检查数据点编码是否重复")
    public Result<Boolean> checkDataPointCodeExists(
            @ApiParam(value = "数据点编码", required = true)
            @PathVariable @NotNull(message = "数据点编码不能为空") String code) {
        log.info("检查数据点编码是否存在请求，编码：{}", code);
        return dataPointService.checkDataPointCodeExists(code);
    }
    
    @GetMapping("/count")
    @ApiOperation(value = "统计数据点数量", notes = "统计数据点总数")
    public Result<Long> countDataPoints() {
        log.info("统计数据点数量请求");
        return dataPointService.countDataPoints();
    }
    
    /**
     * 关联数据点
     *
     * @param deviceId 设备ID
     * @param dataPointDTO 数据点信息
     * @return 关联结果
     */
    @PostMapping("/device/{deviceId}/datapoints")
    @ApiOperation(value = "关联数据点", notes = "为设备关联数据点")
    public Result<DataPointBO> associateDataPoint(
            @ApiParam(value = "设备ID", required = true)
            @PathVariable @NotNull(message = "设备ID不能为空") Long deviceId,
            @ApiParam(value = "数据点信息", required = true)
            @Valid @RequestBody DataPointDTO dataPointDTO) {
        log.info("关联数据点请求，设备ID：{}，数据点信息：{}", deviceId, dataPointDTO);
        // 设置设备ID
        dataPointDTO.setDeviceId(deviceId);
        return dataPointService.createDataPoint(dataPointDTO);
    }
    
    /**
     * 获取设备数据点
     *
     * @param deviceId 设备ID
     * @return 数据点列表
     */
    @GetMapping("/device/{deviceId}/datapoints")
    @ApiOperation(value = "获取设备数据点", notes = "获取设备关联的数据点列表")
    public Result<List<DataPointBO>> getDeviceDataPoints(
            @ApiParam(value = "设备ID", required = true)
            @PathVariable @NotNull(message = "设备ID不能为空") Long deviceId) {
        log.info("获取设备数据点请求，设备ID：{}", deviceId);
        return Result.success(dataPointService.listDataPointsByDevice(deviceId));
    }
}
// @AI-Generated-end 
