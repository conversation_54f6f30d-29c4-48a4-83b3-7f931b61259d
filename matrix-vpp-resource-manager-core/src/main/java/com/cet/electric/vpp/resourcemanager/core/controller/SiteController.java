// @AI-Generated-start
package com.cet.electric.vpp.resourcemanager.core.controller;


import com.cet.electric.vpp.resourcemanager.common.bean.Result;
import com.cet.electric.vpp.resourcemanager.common.bean.bo.SiteBO;
import com.cet.electric.vpp.resourcemanager.common.bean.dto.SiteDTO;
import com.cet.electric.vpp.resourcemanager.common.bean.dto.SiteQueryDTO;
import com.cet.electric.vpp.resourcemanager.common.common.PageResult;
import com.cet.electric.vpp.resourcemanager.core.service.SiteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 站点控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/vpp/resource-manager/site-config")
@Api(tags = "站点管理", description = "站点相关接口")
@Validated
public class SiteController {

    @Autowired
    private SiteService siteService;

    /**
     * 创建站点
     *
     * @param siteDTO 站点信息
     * @return 创建结果
     */
    @PostMapping
    @ApiOperation(value = "创建站点", notes = "创建新站点")
    public Result<SiteBO> createSite(
            @ApiParam(value = "站点信息", required = true)
            @Valid @RequestBody SiteDTO siteDTO) {
        log.info("创建站点请求，参数：{}", siteDTO);
        return siteService.createSite(siteDTO);
    }

    /**
     * 根据ID查询站点
     *
     * @param id 站点ID
     * @return 站点信息
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询站点", notes = "根据站点ID查询详细信息")
    public Result<SiteBO> getSiteById(
            @ApiParam(value = "站点ID", required = true)
            @PathVariable @NotNull(message = "站点ID不能为空") Long id) {
        log.info("查询站点请求，ID：{}", id);
        return siteService.getSiteById(id);
    }

    /**
     * 根据编码查询站点
     *
     * @param code 站点编码
     * @return 站点信息
     */
    @GetMapping("/code/{code}")
    @ApiOperation(value = "根据编码查询站点", notes = "根据站点编码查询详细信息")
    public Result<SiteBO> getSiteByCode(
            @ApiParam(value = "站点编码", required = true)
            @PathVariable @NotNull(message = "站点编码不能为空") String code) {
        log.info("根据编码查询站点请求，编码：{}", code);
        return siteService.getSiteByCode(code);
    }

    /**
     * 更新站点信息
     *
     * @param id      站点ID
     * @param siteDTO 站点信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新站点", notes = "更新站点信息")
    public Result<SiteBO> updateSite(
            @ApiParam(value = "站点ID", required = true)
            @PathVariable @NotNull(message = "站点ID不能为空") Long id,
            @ApiParam(value = "站点信息", required = true)
            @Valid @RequestBody SiteDTO siteDTO) {
        log.info("更新站点请求，ID：{}，参数：{}", id, siteDTO);
        return siteService.updateSite(id, siteDTO);
    }

    /**
     * 删除站点
     *
     * @param id 站点ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除站点", notes = "删除站点")
    public Result<Boolean> deleteSite(
            @ApiParam(value = "站点ID", required = true)
            @PathVariable @NotNull(message = "站点ID不能为空") Long id) {
        log.info("删除站点请求，ID：{}", id);
        return siteService.deleteSite(id);
    }

    /**
     * 分页查询站点列表
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询站点列表", notes = "根据条件分页查询站点列表")
    public Result<PageResult<SiteBO>> getSitePage(
            @ApiParam(value = "查询条件", required = true)
            @Valid @RequestBody SiteQueryDTO queryDTO) {
        log.info("分页查询站点列表请求，参数：{}", queryDTO);
        return siteService.getSiteList(queryDTO);
    }

    /**
     * 查询所有站点列表
     *
     * @return 站点列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询所有站点列表", notes = "查询所有站点列表（不分页）")
    public Result<List<SiteBO>> getAllSites() {
        log.info("查询所有站点列表请求");
        return siteService.getAllSites();
    }

    /**
     * 根据VPP查询站点列表
     *
     * @param vppId VPP ID
     * @return 站点列表
     */
    @GetMapping("/vpp/{vppId}")
    @ApiOperation(value = "根据VPP查询站点列表", notes = "根据VPP ID查询站点列表")
    public Result<List<SiteBO>> getSitesByVpp(
            @ApiParam(value = "VPP ID", required = true)
            @PathVariable @NotNull(message = "VPP ID不能为空") Long vppId) {
        log.info("根据VPP查询站点列表请求，VPP ID：{}", vppId);
        return siteService.getSitesByVppId(vppId);
    }

    /**
     * 根据资源查询站点列表
     *
     * @param resourceId 资源ID
     * @return 站点列表
     */
    @GetMapping("/resource/{resourceId}")
    @ApiOperation(value = "根据资源查询站点列表", notes = "根据资源ID查询站点列表")
    public Result<List<SiteBO>> getSitesByResource(
            @ApiParam(value = "资源ID", required = true)
            @PathVariable @NotNull(message = "资源ID不能为空") Long resourceId) {
        log.info("根据资源查询站点列表请求，资源ID：{}", resourceId);
        return siteService.getSitesByResource(resourceId);
    }

    /**
     * 根据类型查询站点列表
     *
     * @param type 站点类型
     * @return 站点列表
     */
    @GetMapping("/type/{type}")
    @ApiOperation(value = "根据类型查询站点列表", notes = "根据站点类型查询列表")
    public Result<List<SiteBO>> getSitesByType(
            @ApiParam(value = "站点类型", required = true)
            @PathVariable @NotNull(message = "站点类型不能为空") Integer type) {
        log.info("根据类型查询站点列表请求，类型：{}", type);
        return siteService.getSitesByType(type);
    }

    /**
     * 根据状态查询站点列表
     *
     * @param status 站点状态
     * @return 站点列表
     */
    @GetMapping("/status/{status}")
    @ApiOperation(value = "根据状态查询站点列表", notes = "根据站点状态查询列表")
    public Result<List<SiteBO>> getSitesByStatus(
            @ApiParam(value = "站点状态", required = true)
            @PathVariable @NotNull(message = "站点状态不能为空") Integer status) {
        log.info("根据状态查询站点列表请求，状态：{}", status);
        return siteService.getSitesByStatus(status);
    }

    /**
     * 检查站点编号是否存在
     *
     * @param code 站点编号
     * @return 是否存在
     */
    @GetMapping("/check-code/{code}")
    @ApiOperation(value = "检查站点编号是否存在", notes = "检查站点编号是否已存在")
    public Result<Boolean> checkSiteCodeExists(
            @ApiParam(value = "站点编号", required = true)
            @PathVariable @NotNull(message = "站点编号不能为空") String code) {
        log.info("检查站点编号是否存在请求，编号：{}", code);
        return siteService.checkSiteCodeExists(code);
    }

    /**
     * 统计站点数量
     *
     * @return 站点数量
     */
    @GetMapping("/count")
    @ApiOperation(value = "统计站点数量", notes = "统计站点总数")
    public Result<Long> countSites() {
        log.info("统计站点数量请求");
        return siteService.countSites();
    }
}
// @AI-Generated-end 
