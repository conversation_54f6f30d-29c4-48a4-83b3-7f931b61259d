// // @AI-Generated-start
// package com.cet.electric.vpp.resourcemanager.core.service;



// import com.cet.electric.vpp.resourcemanager.common.bean.Result;
// import com.cet.electric.vpp.resourcemanager.common.bean.dto.RealTimeDataDTO;

// import java.util.List;
// import java.util.Map;

// /**
//  * 实时数据服务接口
//  * 
//  * <AUTHOR>
//  * @since 2024-01-01
//  */
// public interface RealTimeDataService {

//     /**
//      * 获取单个测点的实时数据
//      *
//      * @param measId 测点ID
//      * @return 实时数据
//      */
//     RealTimeDataDTO getRealTimeData(Long measId);

//     /**
//      * 批量获取测点的实时数据
//      *
//      * @param measIds 测点ID列表
//      * @return 实时数据映射
//      */
//     Map<Long, RealTimeDataDTO> getRealTimeDataBatch(List<Long> measIds);

//     /**
//      * 获取设备的实时数据
//      *
//      * @param deviceId 设备ID
//      * @return 实时数据映射
//      */
//     Map<Long, RealTimeDataDTO> getDeviceRealTimeData(Long deviceId);

//     /**
//      * 获取站点的实时数据
//      *
//      * @param siteId 站点ID
//      * @return 实时数据映射
//      */
//     Map<Long, RealTimeDataDTO> getSiteRealTimeData(Long siteId);

//     /**
//      * 订阅实时数据
//      *
//      * @param measIds 测点ID列表
//      * @param callback 回调接口
//      */
//     void subscribeRealTimeData(List<Long> measIds, RealTimeDataCallback callback);

//     /**
//      * 取消订阅实时数据
//      *
//      * @param measIds 测点ID列表
//      */
//     void unsubscribeRealTimeData(List<Long> measIds);

//     /**
//      * 查询实时数据
//      *
//      * @param measidList 测点ID列表
//      * @return 实时数据结果
//      */
//     Result<Map<String, Double>> queryRealtimedataByMeasids(List<Long> measidList);

//     /**
//      * 批量查询实时数据
//      *
//      * @param dataIdLogicalIdList 数据ID和逻辑ID列表
//      * @return 实时数据结果
//      */
//     Result<List<RealTimeValueBO>> queryRealtimedataBatch(List<DeviceDataIdLogicalIdBO> dataIdLogicalIdList);

//     /**
//      * 实时数据回调接口
//      */
//     interface RealTimeDataCallback {
//         /**
//          * 数据更新回调
//          *
//          * @param data 更新的数据
//          */
//         void onDataUpdate(Map<Long, RealTimeDataDTO> data);
//     }
// }
// // @AI-Generated-end 