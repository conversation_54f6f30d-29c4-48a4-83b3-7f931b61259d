// @AI-Generated-start
package com.cet.electric.vpp.resourcemanager.core.controller;


import com.cet.electric.vpp.resourcemanager.common.bean.Result;
import com.cet.electric.vpp.resourcemanager.common.bean.bo.UserBO;
import com.cet.electric.vpp.resourcemanager.common.bean.bo.VppBO;
import com.cet.electric.vpp.resourcemanager.common.bean.dto.VppDTO;
import com.cet.electric.vpp.resourcemanager.common.bean.dto.VppQueryDTO;
import com.cet.electric.vpp.resourcemanager.common.common.PageResult;
import com.cet.electric.vpp.resourcemanager.core.service.VppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 虚拟电厂控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/vpp/resource-manager/vpp-config")
@Api(tags = "虚拟电厂管理", description = "虚拟电厂相关接口")
@Validated
public class VppController {

    @Autowired
    private VppService vppService;

    /**
     * 创建虚拟电厂
     *
     * @param vppDTO 虚拟电厂信息
     * @return 创建结果
     */
    @PostMapping
    @ApiOperation(value = "创建虚拟电厂", notes = "创建新虚拟电厂")
    public Result<VppBO> createVpp(
            @ApiParam(value = "虚拟电厂信息", required = true)
            @Valid @RequestBody VppDTO vppDTO) {
        log.info("创建虚拟电厂请求，参数：{}", vppDTO);
        return vppService.createVpp(vppDTO);
    }

    /**
     * 根据ID查询虚拟电厂
     *
     * @param id 虚拟电厂ID
     * @return 虚拟电厂信息
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询虚拟电厂", notes = "根据虚拟电厂ID查询详细信息")
    public Result<VppBO> getVppById(
            @ApiParam(value = "虚拟电厂ID", required = true)
            @PathVariable @NotNull(message = "虚拟电厂ID不能为空") Long id) {
        log.info("查询虚拟电厂请求，ID：{}", id);
        return vppService.getVppById(id);
    }

    /**
     * 根据编码查询虚拟电厂
     *
     * @param code 虚拟电厂编码
     * @return 虚拟电厂信息
     */
    @GetMapping("/code/{code}")
    @ApiOperation(value = "根据编码查询虚拟电厂", notes = "根据虚拟电厂编码查询详细信息")
    public Result<VppBO> getVppByCode(
            @ApiParam(value = "虚拟电厂编码", required = true)
            @PathVariable @NotNull(message = "虚拟电厂编码不能为空") String code) {
        log.info("根据编码查询虚拟电厂请求，编码：{}", code);
        return vppService.getVppByCode(code);
    }

    /**
     * 更新虚拟电厂信息
     *
     * @param id     虚拟电厂ID
     * @param vppDTO 虚拟电厂信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新虚拟电厂", notes = "更新虚拟电厂信息")
    public Result<VppBO> updateVpp(
            @ApiParam(value = "虚拟电厂ID", required = true)
            @PathVariable @NotNull(message = "虚拟电厂ID不能为空") Long id,
            @ApiParam(value = "虚拟电厂信息", required = true)
            @Valid @RequestBody VppDTO vppDTO) {
        log.info("更新虚拟电厂请求，ID：{}，参数：{}", id, vppDTO);
        return vppService.updateVpp(id, vppDTO);
    }

    /**
     * 删除虚拟电厂
     *
     * @param id 虚拟电厂ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除虚拟电厂", notes = "删除虚拟电厂")
    public Result<Boolean> deleteVpp(
            @ApiParam(value = "虚拟电厂ID", required = true)
            @PathVariable @NotNull(message = "虚拟电厂ID不能为空") Long id) {
        log.info("删除虚拟电厂请求，ID：{}", id);
        return vppService.deleteVpp(id);
    }

    /**
     * 分页查询虚拟电厂列表
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询虚拟电厂列表", notes = "根据条件分页查询虚拟电厂列表")
    public Result<PageResult<VppBO>> getVppPage(
            @ApiParam(value = "查询条件", required = true)
            @Valid @RequestBody VppQueryDTO queryDTO) {
        log.info("分页查询虚拟电厂列表请求，参数：{}", queryDTO);
        return vppService.getVppPage(queryDTO);
    }

    /**
     * 查询所有虚拟电厂列表
     *
     * @return 虚拟电厂列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询所有虚拟电厂列表", notes = "查询所有虚拟电厂列表（不分页）")
    public Result<List<VppBO>> getAllVpps() {
        log.info("查询所有虚拟电厂列表请求");
        return vppService.getAllVpps();
    }

    /**
     * 根据类型查询虚拟电厂列表
     *
     * @param type 虚拟电厂类型
     * @return 虚拟电厂列表
     */
    @GetMapping("/type/{type}")
    @ApiOperation(value = "根据类型查询虚拟电厂列表", notes = "根据虚拟电厂类型查询列表")
    public Result<List<VppBO>> getVppsByType(
            @ApiParam(value = "虚拟电厂类型", required = true)
            @PathVariable @NotNull(message = "虚拟电厂类型不能为空") Integer type) {
        log.info("根据类型查询虚拟电厂列表请求，类型：{}", type);
        return vppService.getVppsByType(type);
    }

    /**
     * 根据状态查询虚拟电厂列表
     *
     * @param status 虚拟电厂状态
     * @return 虚拟电厂列表
     */
    @GetMapping("/status/{status}")
    @ApiOperation(value = "根据状态查询虚拟电厂列表", notes = "根据虚拟电厂状态查询列表")
    public Result<List<VppBO>> getVppsByStatus(
            @ApiParam(value = "虚拟电厂状态", required = true)
            @PathVariable @NotNull(message = "虚拟电厂状态不能为空") Integer status) {
        log.info("根据状态查询虚拟电厂列表请求，状态：{}", status);
        return vppService.getVppsByStatus(status);
    }

    /**
     * 检查虚拟电厂编码是否存在
     *
     * @param code 虚拟电厂编码
     * @return 是否存在
     */
    @GetMapping("/check-code/{code}")
    @ApiOperation(value = "检查虚拟电厂编码是否存在", notes = "检查虚拟电厂编码是否已存在")
    public Result<Boolean> checkVppCodeExists(
            @ApiParam(value = "虚拟电厂编码", required = true)
            @PathVariable @NotNull(message = "虚拟电厂编码不能为空") String code) {
        log.info("检查虚拟电厂编码是否存在请求，编码：{}", code);
        return vppService.checkVppCodeExists(code);
    }

    /**
     * 统计虚拟电厂数量
     *
     * @return 虚拟电厂数量
     */
    @GetMapping("/count")
    @ApiOperation(value = "统计虚拟电厂数量", notes = "统计虚拟电厂总数")
    public Result<Long> countVpps() {
        log.info("统计虚拟电厂数量请求");
        return vppService.countVpps();
    }

    /**
     * 获取虚拟电厂下用户列表
     *
     * @param vppId 虚拟电厂ID
     * @return 用户列表
     */
    @GetMapping("/{vppId}/users")
    @ApiOperation(value = "获取虚拟电厂下用户列表", notes = "根据虚拟电厂ID获取用户列表")
    public Result<List<UserBO>> getUsersByVpp(
            @ApiParam(value = "虚拟电厂ID", required = true)
            @PathVariable @NotNull(message = "虚拟电厂ID不能为空") Long vppId) {
        log.info("获取虚拟电厂下用户列表请求，VPP ID：{}", vppId);
        return vppService.getUsersByVpp(vppId);
    }

    /**
     * 获取省份类型配置
     *
     * @param province 省份
     * @return 类型配置列表
     */
    @GetMapping("/types/{province}")
    @ApiOperation(value = "获取省份类型配置", notes = "根据省份获取虚拟电厂类型配置")
    public Result<List<String>> getVppTypesByProvince(
            @ApiParam(value = "省份", required = true)
            @PathVariable @NotNull(message = "省份不能为空") String province) {
        log.info("获取省份类型配置请求，省份：{}", province);
        return vppService.getVppTypesByProvince(province);
    }
}
// @AI-Generated-end 
