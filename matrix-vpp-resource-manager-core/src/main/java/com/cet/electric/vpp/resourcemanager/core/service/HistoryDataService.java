 // @AI-Generated-start
 package com.cet.electric.vpp.resourcemanager.core.service;



 import com.cet.electric.vpp.resourcemanager.common.bean.Result;
 import com.cet.electric.vpp.resourcemanager.common.bean.dto.HistoryDataDTO;
 import com.cet.electric.vpp.resourcemanager.common.bean.dto.HistoryDataQueryDTO;

 import java.util.List;

 /**
  * 历史数据服务接口
  *
  * <AUTHOR>
  * @since 2024-01-01
  */
 public interface HistoryDataService {

     /**
      * 查询历史数据
      *
      * @param queryDTO 查询条件
      * @return 历史数据列表
      */
     Result<List<HistoryDataDTO>> queryHistoryData(HistoryDataQueryDTO queryDTO);

     /**
      * 查询指定时间范围的历史数据
      *
      * @param measId 测点ID
      * @param startTime 开始时间
      * @param endTime 结束时间
      * @param interval 时间间隔（秒）
      * @return 历史数据列表
      */
     Result<List<HistoryDataDTO>> queryHistoryDataByTimeRange(Long measId, Long startTime, Long endTime, Integer interval);

     /**
      * 查询设备历史数据
      *
      * @param deviceId 设备ID
      * @param startTime 开始时间
      * @param endTime 结束时间
      * @return 历史数据列表
      */
     Result<List<HistoryDataDTO>> queryDeviceHistoryData(Long deviceId, Long startTime, Long endTime);

     /**
      * 查询站点历史数据
      *
      * @param siteId 站点ID
      * @param startTime 开始时间
      * @param endTime 结束时间
      * @return 历史数据列表
      */
     Result<List<HistoryDataDTO>> querySiteHistoryData(Long siteId, Long startTime, Long endTime);

     /**
      * 查询测点历史数据
      *
      * @param startTime 开始时间
      * @param endTime 结束时间
      * @param interval 时间间隔（秒）
      * @param queryDTO 查询条件
      * @return 历史数据列表
      */
     Result<List<HistoryDataDTO>> getPointHistoryData(Long startTime, Long endTime, Integer interval, HistoryDataQueryDTO queryDTO);

     /**
      * 查询设备历史数据
      *
      * @param startTime 开始时间
      * @param endTime 结束时间
      * @param interval 时间间隔（秒）
      * @param queryDTO 查询条件
      * @return 历史数据列表
      */
     Result<List<HistoryDataDTO>> getDeviceHistoryData(Long startTime, Long endTime, Integer interval, HistoryDataQueryDTO queryDTO);

     /**
      * 查询站点历史数据
      *
      * @param startTime 开始时间
      * @param endTime 结束时间
      * @param interval 时间间隔（秒）
      * @param queryDTO 查询条件
      * @return 历史数据列表
      */
     Result<List<HistoryDataDTO>> getSiteHistoryData(Long startTime, Long endTime, Integer interval, HistoryDataQueryDTO queryDTO);

     /**
      * 导出历史数据
      *
      * @param queryDTO 查询条件
      * @return 导出文件路径
      */
     Result<String> exportHistoryData(HistoryDataQueryDTO queryDTO);
 }
 // @AI-Generated-end