// @AI-Generated-start
package com.cet.electric.vpp.resourcemanager.core.controller;


import com.cet.electric.vpp.resourcemanager.common.bean.Result;
import com.cet.electric.vpp.resourcemanager.common.bean.bo.DeviceBO;
import com.cet.electric.vpp.resourcemanager.common.bean.dto.DeviceDTO;
import com.cet.electric.vpp.resourcemanager.common.bean.dto.DeviceQueryDTO;
import com.cet.electric.vpp.resourcemanager.common.common.PageResult;
import com.cet.electric.vpp.resourcemanager.core.service.DeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 设备管理控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/vpp/resource-manager/device-config")
@Api(tags = "设备管理", description = "设备相关接口")
@Validated
public class DeviceController {
    
    @Autowired
    private DeviceService deviceService;
    
    @PostMapping
    @ApiOperation(value = "创建设备", notes = "创建新设备")
    public Result<DeviceBO> createDevice(@Valid @RequestBody DeviceDTO deviceDTO) {
        log.info("创建设备请求，参数：{}", deviceDTO);
        return deviceService.createDevice(deviceDTO);
    }
    
    @GetMapping("/{id}")
    @ApiOperation(value = "获取设备详情", notes = "根据ID获取设备详细信息")
    public Result<DeviceBO> getDeviceById(
            @ApiParam(value = "设备ID") 
            @PathVariable @NotNull(message = "设备ID不能为空") Long id) {
        log.info("获取设备详情请求，ID：{}", id);
        return deviceService.getDeviceById(id);
    }
    
    @PutMapping("/{id}")
    @ApiOperation(value = "更新设备", notes = "更新设备信息")
    public Result<DeviceBO> updateDevice(
            @ApiParam(value = "设备ID") 
            @PathVariable @NotNull(message = "设备ID不能为空") Long id,
            @Valid @RequestBody DeviceDTO deviceDTO) {
        log.info("更新设备请求，ID：{}，参数：{}", id, deviceDTO);
        return deviceService.updateDevice(id, deviceDTO);
    }
    
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除设备", notes = "删除指定的设备")
    public Result<Boolean> deleteDevice(
            @ApiParam(value = "设备ID") 
            @PathVariable @NotNull(message = "设备ID不能为空") Long id) {
        log.info("删除设备请求，ID：{}", id);
        return deviceService.deleteDevice(id);
    }
    
    @GetMapping
    @ApiOperation(value = "分页查询设备列表", notes = "根据条件分页查询设备列表")
    public Result<PageResult<DeviceBO>> getDeviceList(@Valid DeviceQueryDTO queryDTO) {
        log.info("分页查询设备列表请求，参数：{}", queryDTO);
        return deviceService.getDeviceList(queryDTO);
    }
    
    @GetMapping("/site/{siteId}")
    @ApiOperation(value = "获取站点下设备列表", notes = "根据站点ID获取设备列表")
    public Result<List<DeviceBO>> getDevicesBySiteId(
            @ApiParam(value = "站点ID") 
            @PathVariable @NotNull(message = "站点ID不能为空") Long siteId) {
        log.info("获取站点下设备列表请求，站点ID：{}", siteId);
        return deviceService.getDevicesBySiteId(siteId);
    }
    
    @GetMapping("/check-id")
    @ApiOperation(value = "检查设备编号是否存在", notes = "检查设备编号是否重复")
    public Result<Boolean> checkDeviceIdExists(
            @ApiParam(value = "设备编号") 
            @RequestParam @NotNull(message = "设备编号不能为空") String deviceId,
            @ApiParam(value = "排除的ID") 
            @RequestParam(required = false) Long excludeId) {
        log.info("检查设备编号是否存在请求，设备编号：{}，排除ID：{}", deviceId, excludeId);
        return deviceService.checkDeviceIdExists(deviceId, excludeId);
    }
    
    @PutMapping("/{id}/statistics")
    @ApiOperation(value = "更新设备统计信息", notes = "更新设备的相关统计信息")
    public Result<Boolean> updateDeviceStatistics(
            @ApiParam(value = "设备ID") 
            @PathVariable @NotNull(message = "设备ID不能为空") Long id) {
        log.info("更新设备统计信息请求，ID：{}", id);
        return deviceService.updateDeviceStatistics(id);
    }
}
// @AI-Generated-end 
