// @AI-Generated-start
package com.cet.electric.vpp.resourcemanager.core.controller;


import com.cet.electric.vpp.resourcemanager.common.bean.Result;
import com.cet.electric.vpp.resourcemanager.common.bean.dto.HistoryDataDTO;
import com.cet.electric.vpp.resourcemanager.common.bean.dto.HistoryDataQueryDTO;
import com.cet.electric.vpp.resourcemanager.core.service.HistoryDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 历史数据控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/vpp/resource-manager/history")
@Api(tags = "历史数据管理", description = "历史数据相关接口")
@Validated
public class HistoryDataController {

    @Autowired
    private HistoryDataService historyDataService;

    /**
     * 查询指定时间范围的历史数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param queryDTO 查询条件
     * @return 历史数据列表
     */
    @PostMapping("/point")
    @ApiOperation(value = "查询测点历史数据", notes = "查询指定测点在时间范围内的历史数据")
    public Result<List<HistoryDataDTO>> getPointHistoryData(
            @ApiParam(value = "开始时间", required = true)
            @RequestParam @NotNull(message = "开始时间不能为空") Long startTime,
            @ApiParam(value = "结束时间", required = true)
            @RequestParam @NotNull(message = "结束时间不能为空") Long endTime,
            @ApiParam(value = "时间间隔（秒）")
            @RequestParam(required = false) Integer interval,
            @Valid @RequestBody HistoryDataQueryDTO queryDTO) {
        log.info("查询测点历史数据，请求参数：startTime={}, endTime={}, interval={}, queryDTO={}", startTime, endTime, interval, queryDTO);
        return historyDataService.getPointHistoryData(startTime, endTime, interval, queryDTO);
    }

    /**
     * 查询设备历史数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param queryDTO 查询条件
     * @return 历史数据列表
     */
    @PostMapping("/device")
    @ApiOperation(value = "查询设备历史数据", notes = "查询指定设备在时间范围内的历史数据")
    public Result<List<HistoryDataDTO>> getDeviceHistoryData(
            @ApiParam(value = "开始时间", required = true)
            @RequestParam @NotNull(message = "开始时间不能为空") Long startTime,
            @ApiParam(value = "结束时间", required = true)
            @RequestParam @NotNull(message = "结束时间不能为空") Long endTime,
            @ApiParam(value = "时间间隔（秒）")
            @RequestParam(required = false) Integer interval,
            @Valid @RequestBody HistoryDataQueryDTO queryDTO) {
        log.info("查询设备历史数据，请求参数：startTime={}, endTime={}, interval={}, queryDTO={}", startTime, endTime, interval, queryDTO);
        return historyDataService.getDeviceHistoryData(startTime, endTime, interval, queryDTO);
    }

    /**
     * 查询站点历史数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param queryDTO 查询条件
     * @return 历史数据列表
     */
    @PostMapping("/site")
    @ApiOperation(value = "查询站点历史数据", notes = "查询指定站点在时间范围内的历史数据")
    public Result<List<HistoryDataDTO>> getSiteHistoryData(
            @ApiParam(value = "开始时间", required = true)
            @RequestParam @NotNull(message = "开始时间不能为空") Long startTime,
            @ApiParam(value = "结束时间", required = true)
            @RequestParam @NotNull(message = "结束时间不能为空") Long endTime,
            @ApiParam(value = "时间间隔（秒）")
            @RequestParam(required = false) Integer interval,
            @Valid @RequestBody HistoryDataQueryDTO queryDTO) {
        log.info("查询站点历史数据，请求参数：startTime={}, endTime={}, interval={}, queryDTO={}", startTime, endTime, interval, queryDTO);
        return historyDataService.getSiteHistoryData(startTime, endTime, interval, queryDTO);
    }

    /**
     * 导出历史数据
     *
     * @param queryDTO 查询条件
     * @return 导出结果
     */
    @PostMapping("/export")
    @ApiOperation(value = "导出历史数据", notes = "根据条件导出历史数据到文件")
    public Result<String> exportHistoryData(
            @Valid @RequestBody HistoryDataQueryDTO queryDTO) {
        log.info("导出历史数据，请求参数：{}", queryDTO);
        return historyDataService.exportHistoryData(queryDTO);
    }
}
// @AI-Generated-end 
