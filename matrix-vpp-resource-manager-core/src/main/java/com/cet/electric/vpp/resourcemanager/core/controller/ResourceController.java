// @AI-Generated-start
package com.cet.electric.vpp.resourcemanager.core.controller;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.cet.electric.vpp.resourcemanager.common.bean.Result;
import com.cet.electric.vpp.resourcemanager.common.bean.bo.ResourceBO;
import com.cet.electric.vpp.resourcemanager.common.bean.bo.SiteBO;
import com.cet.electric.vpp.resourcemanager.common.bean.dto.ResourceDTO;
import com.cet.electric.vpp.resourcemanager.common.bean.dto.ResourceQueryDTO;
import com.cet.electric.vpp.resourcemanager.common.common.PageResult;
import com.cet.electric.vpp.resourcemanager.core.service.ResourceService;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 资源管理控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/vpp/resource-manager/resource-config")
@Api(tags = "资源管理", description = "资源相关接口")
@Validated
public class ResourceController {
    
    @Autowired
    private ResourceService resourceService;
    
    @PostMapping
    @ApiOperation(value = "创建资源", notes = "创建新资源")
    public Result<ResourceBO> createResource(
            @ApiParam(value = "资源信息", required = true)
            @Valid @RequestBody ResourceDTO resourceDTO) {
        log.info("创建资源请求，参数：{}", resourceDTO);
        return resourceService.createResource(resourceDTO);
    }
    
    @GetMapping("/{id}")
    @ApiOperation(value = "获取资源详情", notes = "根据ID获取资源详细信息")
    public Result<ResourceBO> getResourceById(
            @ApiParam(value = "资源ID", required = true)
            @PathVariable @NotNull(message = "资源ID不能为空") Long id) {
        log.info("获取资源详情请求，ID：{}", id);
        return resourceService.getResourceById(id);
    }
    
    @GetMapping("/code/{code}")
    @ApiOperation(value = "根据编码获取资源", notes = "根据编码获取资源详细信息")
    public Result<ResourceBO> getResourceByCode(
            @ApiParam(value = "资源编码", required = true)
            @PathVariable @NotNull(message = "资源编码不能为空") String code) {
        log.info("根据编码获取资源请求，编码：{}", code);
        return resourceService.getResourceByCode(code);
    }
    
    @PutMapping("/{id}")
    @ApiOperation(value = "更新资源", notes = "更新资源信息")
    public Result<ResourceBO> updateResource(
            @ApiParam(value = "资源ID", required = true)
            @PathVariable @NotNull(message = "资源ID不能为空") Long id,
            @ApiParam(value = "资源信息", required = true)
            @Valid @RequestBody ResourceDTO resourceDTO) {
        log.info("更新资源请求，ID：{}，参数：{}", id, resourceDTO);
        return resourceService.updateResource(id, resourceDTO);
    }
    
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除资源", notes = "删除指定的资源")
    public Result<Boolean> deleteResource(
            @ApiParam(value = "资源ID", required = true)
            @PathVariable @NotNull(message = "资源ID不能为空") Long id) {
        log.info("删除资源请求，ID：{}", id);
        return resourceService.deleteResource(id);
    }
    
    @PostMapping("/page")
    @ApiOperation(value = "分页查询资源列表", notes = "分页查询资源列表")
    public Result<PageResult<ResourceBO>> getResourceList(
            @ApiParam(value = "查询条件", required = true)
            @Valid @RequestBody ResourceQueryDTO queryDTO) {
        log.info("分页查询资源列表请求，参数：{}", queryDTO);
        return resourceService.getResourceList(queryDTO);
    }
    
    @GetMapping("/list")
    @ApiOperation(value = "获取所有资源列表", notes = "获取所有资源列表")
    public Result<List<ResourceBO>> getAllResources() {
        log.info("获取所有资源列表请求");
        return resourceService.getAllResources();
    }
    
    @GetMapping("/vpp/{vppId}")
    @ApiOperation(value = "获取VPP资源列表", notes = "根据VPP ID获取资源列表")
    public Result<List<ResourceBO>> getResourcesByVppId(
            @ApiParam(value = "VPP ID", required = true)
            @PathVariable @NotNull(message = "VPP ID不能为空") Long vppId) {
        log.info("获取VPP资源列表请求，VPP ID：{}", vppId);
        return resourceService.getResourcesByVppId(vppId);
    }
    
    @GetMapping("/type/{type}")
    @ApiOperation(value = "根据类型获取资源列表", notes = "根据资源类型获取列表")
    public Result<List<ResourceBO>> getResourcesByType(
            @ApiParam(value = "资源类型", required = true)
            @PathVariable @NotNull(message = "资源类型不能为空") Integer type) {
        log.info("根据类型获取资源列表请求，类型：{}", type);
        return resourceService.getResourcesByType(type);
    }
    
    @GetMapping("/status/{status}")
    @ApiOperation(value = "根据状态获取资源列表", notes = "根据资源状态获取列表")
    public Result<List<ResourceBO>> getResourcesByStatus(
            @ApiParam(value = "资源状态", required = true)
            @PathVariable @NotNull(message = "资源状态不能为空") Integer status) {
        log.info("根据状态获取资源列表请求，状态：{}", status);
        return resourceService.getResourcesByStatus(status);
    }
    
    @GetMapping("/check-code/{code}")
    @ApiOperation(value = "检查资源编码是否存在", notes = "检查资源编码是否重复")
    public Result<Boolean> checkResourceCodeExists(
            @ApiParam(value = "资源编码", required = true)
            @PathVariable @NotNull(message = "资源编码不能为空") String code) {
        log.info("检查资源编码是否存在请求，编码：{}", code);
        return resourceService.checkResourceCodeExists(code);
    }
    
    @GetMapping("/count")
    @ApiOperation(value = "统计资源数量", notes = "统计资源总数")
    public Result<Long> countResources() {
        log.info("统计资源数量请求");
        return resourceService.countResources();
    }
    
    /**
     * 获取资源下站点列表
     *
     * @param resourceId 资源ID
     * @return 站点列表
     */
    @GetMapping("/{resourceId}/sites")
    @ApiOperation(value = "获取资源下站点列表", notes = "根据资源ID获取站点列表")
    public Result<List<SiteBO>> getSitesByResource(
            @ApiParam(value = "资源ID", required = true)
            @PathVariable @NotNull(message = "资源ID不能为空") Long resourceId) {
        log.info("获取资源下站点列表请求，资源ID：{}", resourceId);
        return resourceService.getSitesByResource(resourceId);
    }
}
// @AI-Generated-end 
