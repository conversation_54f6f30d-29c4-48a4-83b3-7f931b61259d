// @AI-Generated-start
package com.cet.electric.vpp.resourcemanager.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备类型枚举
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Getter
@AllArgsConstructor
public enum DeviceTypeEnum {

    /**
     * 光伏发电设备
     */
    SOLAR_PV(1, "光伏发电设备", "太阳能光伏发电设备"),

    /**
     * 风力发电设备
     */
    WIND_TURBINE(2, "风力发电设备", "风力发电机组"),

    /**
     * 储能设备
     */
    ENERGY_STORAGE(3, "储能设备", "电池储能系统"),

    /**
     * 充电桩
     */
    CHARGING_STATION(4, "充电桩", "电动汽车充电设备"),

    /**
     * 空调设备
     */
    AIR_CONDITIONER(5, "空调设备", "楼宇空调系统"),

    /**
     * 照明设备
     */
    LIGHTING(6, "照明设备", "智能照明系统"),

    /**
     * 工业负荷
     */
    INDUSTRIAL_LOAD(7, "工业负荷", "工业可调节负荷"),

    /**
     * 商业负荷
     */
    COMMERCIAL_LOAD(8, "商业负荷", "商业可调节负荷"),

    /**
     * 其他设备
     */
    OTHER(99, "其他设备", "其他类型设备");

    /**
     * 类型值
     */
    private final Integer value;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据值获取枚举
     *
     * @param value 类型值
     * @return 枚举
     */
    public static DeviceTypeEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (DeviceTypeEnum type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据值获取名称
     *
     * @param value 类型值
     * @return 名称
     */
    public static String getName(Integer value) {
        DeviceTypeEnum type = getByValue(value);
        return type != null ? type.getName() : null;
    }

    /**
     * 根据值获取描述
     *
     * @param value 类型值
     * @return 描述
     */
    public static String getDescription(Integer value) {
        DeviceTypeEnum type = getByValue(value);
        return type != null ? type.getDescription() : null;
    }
}
// @AI-Generated-end 