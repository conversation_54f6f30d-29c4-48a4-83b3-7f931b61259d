package com.cet.electric.vpp.resourcemanager.common.bean;

import java.io.Serializable;


import com.cet.electric.vpp.resourcemanager.common.constant.BaseRTC;
import com.cet.electric.vpp.resourcemanager.common.util.LanguageUtil;
import lombok.Data;

@Data
public class Result<T> implements Serializable {
    private static final long serialVersionUID = -5254578483784136934L;
    private int code;
    private String msg;
    private T data;
    private int total = 0;

    public Result() {
        this.code = BaseRTC.SUCCESS;
        this.msg = "执行成功";
    }

    public Result(T data) {
        this.code = BaseRTC.SUCCESS;
        this.msg = "执行成功";
        this.data = data;
    }

    public Result(T data, int total) {
        this.code = BaseRTC.SUCCESS;
        this.msg = "执行成功";
        this.data = data;
        this.total = total;
    }

    public Result(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public Result(int code, String msg, T data, int total) {
        this.code = code;
        this.msg = msg;
        this.data = data;
        this.total = total;
    }

    public Result(Enum enums, T data) {
        String response = enums.toString();
        Integer code = Integer.parseInt(response.substring(response.indexOf("code=") + 5, response.indexOf(",")));
        String msg = response.substring(response.indexOf("msg=") + 4, response.indexOf(")"));

        this.code = code;
        this.msg = LanguageUtil.of(msg, null);
        this.data = data;
    }


    public Result(Enum enums, T data, int total) {
        String response = enums.toString();
        Integer code = Integer.parseInt(response.substring(response.indexOf("code=") + 5, response.indexOf(",")));
        String msg = response.substring(response.indexOf("msg=") + 4, response.indexOf(")"));

        this.code = code;
        this.msg = LanguageUtil.of(msg, null);
        this.data = data;
        this.total = total;
    }

    public Result(Enum enums, Object[] params, T data) {
        String response = enums.toString();
        Integer code = Integer.parseInt(response.substring(response.indexOf("code=") + 5, response.indexOf(",")));
        String msg = response.substring(response.indexOf("msg=") + 4, response.indexOf(")"));

        this.code = code;
        this.msg = LanguageUtil.of(msg, params);
        this.data = data;
    }


    public Result(Enum enums, Object[] params, T data, int total) {
        String response = enums.toString();
        Integer code = Integer.parseInt(response.substring(response.indexOf("code=") + 5, response.indexOf(",")));
        String msg = response.substring(response.indexOf("msg=") + 4, response.indexOf(")"));

        this.code = code;
        this.msg = LanguageUtil.of(msg, params);
        this.data = data;
        this.total = total;
    }

    /**
     * 判断是否请求返回成功
     * @param code 结果code码
     * @return
     */
    public static boolean checkResultSuccessCode(int code) {
        if (code == 0) {
            return true;
        }
        return false;
    }

    /**
     * 返回异常结果
     * @param code 结果code码
     * @return
     */
    public static Result error(int code, String msg) {
        return new Result(code,msg,null);
    }

    /**
     * 返回异常结果
     * @param msg 错误信息
     * @return
     */
    public static <T> Result<T> error(String msg) {
        return new Result<>(BaseRTC.ERROR, msg, null);
    }

    /**
     * 返回成功结果
     * @param data 数据
     * @return
     */
    public static <T> Result<T> success(T data) {
        return new Result<>(data);
    }

    /**
     * 返回成功结果
     * @return
     */
    public static <T> Result<T> success() {
        return new Result<>();
    }

    /**
     * 返回成功结果
     * @param data 数据
     * @param total 总数
     * @return
     */
    public static <T> Result<T> success(T data, int total) {
        return new Result<>(data, total);
    }

    /**
     * 返回异常结果
     * @param msg 错误信息
     * @param data 数据
     * @return
     */
    public static <T> Result<T> error(String msg, T data) {
        return new Result<>(BaseRTC.ERROR, msg, data);
    }

    /**
     * 获取数据
     * @return
     */
    public T getData() {
        return data;
    }

    /**
     * 设置数据
     * @param data
     */
    public void setData(T data) {
        this.data = data;
    }

    /**
     * 获取消息
     * @return
     */
    public String getMessage() {
        return msg;
    }

    /**
     * 设置消息
     * @param msg
     */
    public void setMessage(String msg) {
        this.msg = msg;
    }

    /**
     * 获取总数
     * @return
     */
    public int getTotal() {
        return total;
    }

    /**
     * 设置总数
     * @param total
     */
    public void setTotal(int total) {
        this.total = total;
    }

    /**
     * 获取代码
     * @return
     */
    public int getCode() {
        return code;
    }

    /**
     * 设置代码
     * @param code
     */
    public void setCode(int code) {
        this.code = code;
    }

    /**
     * 判断是否成功
     * @return
     */
    public boolean isSuccess() {
        return checkResultSuccessCode(this.code);
    }

    /**
     * 判断是否失败
     * @return
     */
    public boolean isError() {
        return !checkResultSuccessCode(this.code);
    }
}
