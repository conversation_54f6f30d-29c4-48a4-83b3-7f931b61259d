package com.cet.electric.vpp.resourcemanager.common.util;

import java.nio.charset.StandardCharsets;
import java.util.Locale;

import org.springframework.context.support.ReloadableResourceBundleMessageSource;

import com.cet.electric.vpp.resourcemanager.common.constant.BaseModelLabel;
import com.cet.electric.vpp.resourcemanager.common.constant.CommonConstant;

import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName: LanguageUtil
 * @Description: 语言工具
 * @Author: kyn
 * @Date: 2023/11/7 14:23
 **/
@Slf4j
public class LanguageUtil {

    public static String of(String msg) {
        return of(msg, null);
    }

    public static String of(String msg, Object[] params) {
        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
        messageSource.setCacheSeconds(-1);
        messageSource.setDefaultEncoding(StandardCharsets.UTF_8.name());
        messageSource.setBasename("classpath:i18n/messages");
        messageSource.setUseCodeAsDefaultMessage(true);

        String message = "";
        try {
            message = messageSource.getMessage(msg, params, localeType(BaseModelLabel.TRANSLATE_ENABLE));
        } catch (Exception ex) {
            log.error("parse message error!", ex);
        }
        return message;
    }

    public static Locale localeType(Integer type) {
        if (CommonConstant.TRANSLATE_ENABLE==null){
            type = 1;
        }
        Locale locale = Locale.CHINA;
        switch (type) {
            case 1:
                locale = Locale.CHINA;
                break;
            case 2:
                locale = Locale.US;
                break;
        }
        return locale;
    }
}