// @AI-Generated-start
package com.cet.electric.vpp.resourcemanager.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资源类型枚举
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Getter
@AllArgsConstructor
public enum ResourceTypeEnum {

    /**
     * 发电设备
     */
    GENERATION(1, "发电设备"),

    /**
     * 储能设备
     */
    STORAGE(2, "储能设备"),

    /**
     * 负荷设备
     */
    LOAD(3, "负荷设备");

    /**
     * 类型值
     */
    private final Integer value;

    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据值获取枚举
     *
     * @param value 类型值
     * @return 枚举
     */
    public static ResourceTypeEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (ResourceTypeEnum type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据值获取描述
     *
     * @param value 类型值
     * @return 描述
     */
    public static String getDescription(Integer value) {
        ResourceTypeEnum type = getByValue(value);
        return type != null ? type.getDescription() : null;
    }
}
// @AI-Generated-end 