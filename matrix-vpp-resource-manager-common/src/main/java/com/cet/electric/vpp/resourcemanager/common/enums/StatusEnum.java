// @AI-Generated-start
package com.cet.electric.vpp.resourcemanager.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 状态枚举
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Getter
@AllArgsConstructor
public enum StatusEnum {

    /**
     * 正常
     */
    NORMAL(1, "正常"),

    /**
     * 维护
     */
    MAINTENANCE(2, "维护"),

    /**
     * 故障
     */
    FAULT(3, "故障"),

    /**
     * 离线
     */
    OFFLINE(4, "离线"),

    /**
     * 禁用
     */
    DISABLED(5, "禁用");

    /**
     * 状态值
     */
    private final Integer value;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 枚举
     */
    public static StatusEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (StatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据值获取描述
     *
     * @param value 状态值
     * @return 描述
     */
    public static String getDescription(Integer value) {
        StatusEnum status = getByValue(value);
        return status != null ? status.getDescription() : null;
    }
}
// @AI-Generated-end 