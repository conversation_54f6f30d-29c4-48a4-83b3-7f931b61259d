// @AI-Generated-start
package com.cet.electric.vpp.resourcemanager.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 站点类型枚举
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Getter
@AllArgsConstructor
public enum SiteTypeEnum {
    
    /**
     * 自备电源
     */
    SELF_POWER("01", "自备电源"),
    
    /**
     * 用户侧储能
     */
    USER_STORAGE("02", "用户侧储能"),
    
    /**
     * 电动汽车
     */
    ELECTRIC_VEHICLE("03", "电动汽车"),
    
    /**
     * 充电站
     */
    CHARGING_STATION("04", "充电站"),
    
    /**
     * 换电站
     */
    BATTERY_SWAP_STATION("05", "换电站"),
    
    /**
     * 楼宇空调
     */
    BUILDING_AIR_CONDITIONING("06", "楼宇空调"),
    
    /**
     * 工商业可调节负荷
     */
    COMMERCIAL_LOAD("07", "工商业可调节负荷"),
    
    /**
     * 分布式光伏
     */
    DISTRIBUTED_PV("08", "分布式光伏"),
    
    /**
     * 分散式风电
     */
    DISTRIBUTED_WIND("09", "分散式风电"),
    
    /**
     * 分布式独立储能
     */
    DISTRIBUTED_STORAGE("10", "分布式独立储能"),
    
    /**
     * 非可调节负荷
     */
    NON_ADJUSTABLE_LOAD("11", "非可调节负荷"),
    
    /**
     * 其他
     */
    OTHER("99", "其他");
    
    /**
     * 类型编码
     */
    private final String code;
    
    /**
     * 类型名称
     */
    private final String name;
    
    /**
     * 根据编码获取枚举
     * 
     * @param code 类型编码
     * @return 枚举值
     */
    public static SiteTypeEnum getByCode(String code) {
        for (SiteTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 根据名称获取枚举
     * 
     * @param name 类型名称
     * @return 枚举值
     */
    public static SiteTypeEnum getByName(String name) {
        for (SiteTypeEnum type : values()) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }
}
// @AI-Generated-end 