// @AI-Generated-start
package com.cet.electric.vpp.resourcemanager.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * VPP类型枚举
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Getter
@AllArgsConstructor
public enum VppTypeEnum {

    /**
     * 聚合型
     */
    AGGREGATION(1, "聚合型", "聚合多个分布式能源资源"),

    /**
     * 协调型
     */
    COORDINATION(2, "协调型", "协调多个VPP之间的运行"),

    /**
     * 混合型
     */
    HYBRID(3, "混合型", "兼具聚合和协调功能");

    /**
     * 类型值
     */
    private final Integer value;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据值获取枚举
     *
     * @param value 类型值
     * @return 枚举
     */
    public static VppTypeEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (VppTypeEnum type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据值获取名称
     *
     * @param value 类型值
     * @return 名称
     */
    public static String getName(Integer value) {
        VppTypeEnum type = getByValue(value);
        return type != null ? type.getName() : null;
    }

    /**
     * 根据值获取描述
     *
     * @param value 类型值
     * @return 描述
     */
    public static String getDescription(Integer value) {
        VppTypeEnum type = getByValue(value);
        return type != null ? type.getDescription() : null;
    }
}
// @AI-Generated-end 