// @AI-Generated-start
package com.cet.electric.vpp.resourcemanager.common.constant;

/**
 * 通用常量类
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public class CommonConstant {
    public static Integer TRANSLATE_ENABLE;
    /**
     * 成功状态码
     */
    public static final Integer SUCCESS_CODE = 200;
    
    /**
     * 失败状态码
     */
    public static final Integer ERROR_CODE = 500;
    
    /**
     * 参数错误状态码
     */
    public static final Integer PARAM_ERROR_CODE = 400;
    
    /**
     * 未授权状态码
     */
    public static final Integer UNAUTHORIZED_CODE = 401;
    
    /**
     * 禁止访问状态码
     */
    public static final Integer FORBIDDEN_CODE = 403;
    
    /**
     * 资源不存在状态码
     */
    public static final Integer NOT_FOUND_CODE = 404;
    
    /**
     * 成功消息
     */
    public static final String SUCCESS_MESSAGE = "操作成功";
    
    /**
     * 失败消息
     */
    public static final String ERROR_MESSAGE = "操作失败";
    
    /**
     * 参数错误消息
     */
    public static final String PARAM_ERROR_MESSAGE = "参数错误";
    
    /**
     * 资源不存在消息
     */
    public static final String NOT_FOUND_MESSAGE = "资源不存在";
    
    /**
     * 资源已存在消息
     */
    public static final String ALREADY_EXISTS_MESSAGE = "资源已存在";
    
    /**
     * 默认分页大小
     */
    public static final Integer DEFAULT_PAGE_SIZE = 20;
    
    /**
     * 最大分页大小
     */
    public static final Integer MAX_PAGE_SIZE = 1000;
    
    /**
     * 默认页码
     */
    public static final Integer DEFAULT_PAGE_NUMBER = 1;
    
    /**
     * 逻辑删除标记 - 未删除
     */
    public static final Boolean NOT_DELETED = false;
    
    /**
     * 逻辑删除标记 - 已删除
     */
    public static final Boolean DELETED = true;
    
    /**
     * 是
     */
    public static final Boolean YES = true;
    
    /**
     * 否
     */
    public static final Boolean NO = false;
    
    /**
     * 空字符串
     */
    public static final String EMPTY_STRING = "";
    
    /**
     * 逗号分隔符
     */
    public static final String COMMA_SEPARATOR = ",";
    
    /**
     * 下划线分隔符
     */
    public static final String UNDERSCORE_SEPARATOR = "_";
    
    /**
     * 横线分隔符
     */
    public static final String HYPHEN_SEPARATOR = "-";
    
    /**
     * 点分隔符
     */
    public static final String DOT_SEPARATOR = ".";
    
    /**
     * 冒号分隔符
     */
    public static final String COLON_SEPARATOR = ":";
    
    /**
     * 分号分隔符
     */
    public static final String SEMICOLON_SEPARATOR = ";";
    
    /**
     * 竖线分隔符
     */
    public static final String PIPE_SEPARATOR = "|";
    
    /**
     * 换行符
     */
    public static final String NEW_LINE = "\n";
    
    /**
     * 制表符
     */
    public static final String TAB = "\t";
    
    /**
     * 空格
     */
    public static final String SPACE = " ";
    
    /**
     * 零
     */
    public static final Integer ZERO = 0;
    
    /**
     * 一
     */
    public static final Integer ONE = 1;
    
    /**
     * 十
     */
    public static final Integer TEN = 10;
    
    /**
     * 百
     */
    public static final Integer HUNDRED = 100;
    
    /**
     * 千
     */
    public static final Integer THOUSAND = 1000;
    
    /**
     * 万
     */
    public static final Integer TEN_THOUSAND = 10000;
    
    /**
     * 百万
     */
    public static final Integer MILLION = 1000000;
    
    /**
     * 十亿
     */
    public static final Integer BILLION = 1000000000;
}
// @AI-Generated-end 