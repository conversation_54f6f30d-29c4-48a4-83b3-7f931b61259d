package com.cet.electric.vpp.resourcemanager.common.constant;

/**
 * @Description:
 * @Author: Wisdom
 * @Date: Created in 11:03 2020/12/4
 */
public class BaseRTC {
    // 成功/正常
    public static final int SUCCESS = 0;
    // 失败/异常
    public static final int ERROR = -1;

    public static final int NOT_FOUND = -1;

    public static final int FAILED = 1;

    public static final int NOT_ALLOWED = 2;

    public static final int EXCEPTION = -2000012;

    public static final int PARAM_ERROR = -2000013;

    public static final int WRONG_TOKEN = -2000001;
    //模型写入失败
    public static final int MODEL_WRITE_ERROR = -3000001;
    //模型删除失败
    public static final int MODEL_DELETE_ERROR = -3000002;
    //模型查询失败
    public static final int MODEL_QUERY_ERROR = -3000003;
    //节点服务新增失败
    public static final int PEC_ADD_ERROR = -4000001;
    //节点服务更新失败
    public static final int PEC_UPDATE_ERROR = -4000002;
    //节点服务删除失败
    public static final int PEC_DELETE_ERROR = -4000003;
    //设备数据服务查询失败
    public static final int DEVICEDATA_QUERY_ERROR = -5000003;
    public static final String WRONG_TOKEN_MSG = "令牌错误，请重新登录";
    public static final Object NOTICE_PLATFORM_WRITE_ERROR ="" ;
}
