// @AI-Generated-start
package com.cet.electric.vpp.resourcemanager.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备状态枚举
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Getter
@AllArgsConstructor
public enum DeviceStatusEnum {
    
    /**
     * 无信息
     */
    NO_INFO("无信息", "设备状态无信息"),
    
    /**
     * 离线故障
     */
    OFFLINE_FAULT("离线故障", "设备离线或故障"),
    
    /**
     * 报警
     */
    ALARM("报警", "设备报警"),
    
    /**
     * 正常运行
     */
    NORMAL("正常运行", "设备正常运行"),
    
    /**
     * 占用
     */
    OCCUPIED("占用", "设备被占用"),
    
    /**
     * 空闲
     */
    IDLE("空闲", "设备空闲");
    
    /**
     * 状态名称
     */
    private final String name;
    
    /**
     * 状态描述
     */
    private final String description;
    
    /**
     * 根据名称获取枚举
     * 
     * @param name 状态名称
     * @return 枚举值
     */
    public static DeviceStatusEnum getByName(String name) {
        for (DeviceStatusEnum status : values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        return null;
    }
}
// @AI-Generated-end 