// @AI-Generated-start
package com.cet.electric.vpp.resourcemanager.common.bean.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import java.io.Serializable;

/**
 * 数据点查询DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DataPointQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "测点名称")
    private String pointName;

    @ApiModelProperty(value = "测点编码")
    private String pointCode;

    @ApiModelProperty(value = "测点类型")
    private String pointType;

    @ApiModelProperty(value = "所属设备ID")
    private Long deviceId;

    @ApiModelProperty(value = "所属站点ID")
    private Long siteId;

    @ApiModelProperty(value = "数据类型")
    private String dataType;

    @ApiModelProperty(value = "是否启用")
    private Boolean enabled;

    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "排序字段")
    private String orderBy;

    private String orderDirection;
}
// @AI-Generated-end 
