//// @AI-Generated-start
//package com.cet.electric.vpp.resourcemanager.common.config;
//
//import org.springframework.amqp.core.*;
//import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
//import org.springframework.amqp.rabbit.connection.ConnectionFactory;
//import org.springframework.amqp.rabbit.core.RabbitTemplate;
//import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
///**
// * RabbitMQ配置类
// *
// * <AUTHOR>
// * @since 2024-01-01
// */
//@Configuration
//public class RabbitMQConfig {
//
//    /**
//     * VPP事件队列名称
//     */
//    public static final String VPP_EVENT_QUEUE = "vpp.event.queue";
//
//    /**
//     * VPP事件交换机名称
//     */
//    public static final String VPP_EVENT_EXCHANGE = "vpp.event.exchange";
//
//    /**
//     * VPP事件路由键
//     */
//    public static final String VPP_EVENT_ROUTING_KEY = "vpp.event";
//
//    /**
//     * 设备状态队列名称
//     */
//    public static final String DEVICE_STATUS_QUEUE = "device.status.queue";
//
//    /**
//     * 设备状态交换机名称
//     */
//    public static final String DEVICE_STATUS_EXCHANGE = "device.status.exchange";
//
//    /**
//     * 设备状态路由键
//     */
//    public static final String DEVICE_STATUS_ROUTING_KEY = "device.status";
//
//    /**
//     * 配置消息转换器
//     *
//     * @return Jackson2JsonMessageConverter
//     */
//    @Bean
//    public Jackson2JsonMessageConverter jsonMessageConverter() {
//        return new Jackson2JsonMessageConverter();
//    }
//
//    /**
//     * 配置RabbitTemplate
//     *
//     * @param connectionFactory 连接工厂
//     * @return RabbitTemplate
//     */
//    @Bean
//    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
//        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
//        rabbitTemplate.setMessageConverter(jsonMessageConverter());
//        return rabbitTemplate;
//    }
//
//    /**
//     * 配置监听器容器工厂
//     *
//     * @param connectionFactory 连接工厂
//     * @return SimpleRabbitListenerContainerFactory
//     */
//    @Bean
//    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory(ConnectionFactory connectionFactory) {
//        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
//        factory.setConnectionFactory(connectionFactory);
//        factory.setMessageConverter(jsonMessageConverter());
//        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
//        factory.setPrefetchCount(1);
//        return factory;
//    }
//
//    /**
//     * VPP事件队列
//     *
//     * @return Queue
//     */
//    @Bean
//    public Queue vppEventQueue() {
//        return new Queue(VPP_EVENT_QUEUE, true, false, false);
//    }
//
//    /**
//     * VPP事件交换机
//     *
//     * @return DirectExchange
//     */
//    @Bean
//    public DirectExchange vppEventExchange() {
//        return new DirectExchange(VPP_EVENT_EXCHANGE, true, false);
//    }
//
//    /**
//     * VPP事件绑定
//     *
//     * @return Binding
//     */
//    @Bean
//    public Binding vppEventBinding() {
//        return BindingBuilder.bind(vppEventQueue())
//                .to(vppEventExchange())
//                .with(VPP_EVENT_ROUTING_KEY);
//    }
//
//    /**
//     * 设备状态队列
//     *
//     * @return Queue
//     */
//    @Bean
//    public Queue deviceStatusQueue() {
//        return new Queue(DEVICE_STATUS_QUEUE, true, false, false);
//    }
//
//    /**
//     * 设备状态交换机
//     *
//     * @return DirectExchange
//     */
//    @Bean
//    public DirectExchange deviceStatusExchange() {
//        return new DirectExchange(DEVICE_STATUS_EXCHANGE, true, false);
//    }
//
//    /**
//     * 设备状态绑定
//     *
//     * @return Binding
//     */
//    @Bean
//    public Binding deviceStatusBinding() {
//        return BindingBuilder.bind(deviceStatusQueue())
//                .to(deviceStatusExchange())
//                .with(DEVICE_STATUS_ROUTING_KEY);
//    }
//}
//// @AI-Generated-end