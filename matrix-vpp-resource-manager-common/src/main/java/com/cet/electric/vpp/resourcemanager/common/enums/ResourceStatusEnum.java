// @AI-Generated-start
package com.cet.electric.vpp.resourcemanager.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资源状态枚举
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Getter
@AllArgsConstructor
public enum ResourceStatusEnum {
    
    /**
     * 投运
     */
    OPERATIONAL("投运", "资源已投运"),
    
    /**
     * 测试
     */
    TESTING("测试", "资源处于测试状态"),
    
    /**
     * 退役
     */
    RETIRED("退役", "资源已退役"),
    
    /**
     * 停机
     */
    SHUTDOWN("停机", "资源停机");
    
    /**
     * 状态名称
     */
    private final String name;
    
    /**
     * 状态描述
     */
    private final String description;
    
    /**
     * 根据名称获取枚举
     * 
     * @param name 状态名称
     * @return 枚举值
     */
    public static ResourceStatusEnum getByName(String name) {
        for (ResourceStatusEnum status : values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        return null;
    }
}
// @AI-Generated-end 