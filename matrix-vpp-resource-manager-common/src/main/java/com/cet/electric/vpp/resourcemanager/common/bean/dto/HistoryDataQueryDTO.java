// @AI-Generated-start
package com.cet.electric.vpp.resourcemanager.common.bean.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.util.List;

/**
 * 历史数据查询DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class HistoryDataQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "测点ID列表不能为空")
    private List<Long> measIds;

    @NotNull(message = "开始时间不能为空")
    private Long startTime;

    @NotNull(message = "结束时间不能为空")
    private Long endTime;

    @Min(value = 1, message = "时间间隔必须大于0")
    private Integer interval = 60;

    @ApiModelProperty(value = "是否填充缺失数据")
    private Boolean fill = false;

    private Boolean p35 = false;

    @ApiModelProperty(value = "设备ID")
    private Long deviceId;

    @ApiModelProperty(value = "站点ID")
    private Long siteId;

    @ApiModelProperty(value = "数据类型")
    private String dataType;

    @ApiModelProperty(value = "数据质量")
    private Integer quality;

    /**
     * 当前页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize = 10;
}
// @AI-Generated-end 
